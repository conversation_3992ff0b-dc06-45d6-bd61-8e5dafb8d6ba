{"name": "functionland", "version": "0.0.0", "license": "MIT", "scripts": {"ios": "nx run-ios", "android": "nx run-android", "start": "nx serve", "build": "nx build", "test": "nx run-many --all --skip-nx-cache --target=test", "lint": "nx run-many --all --skip-nx-cache --target=lint", "ensure:symlink": "nx ensure-symlink box && nx ensure-symlink file-manager", "check-app-types": "tsc --noEmit --project apps/box/tsconfig.app.json && tsc --noEmit --project apps/file-manager/tsconfig.app.json", "check-lib-types": "tsc --noEmit --project libs/component-library/tsconfig.lib.json", "check": "yarn lint && yarn test && yarn check-app-types && yarn check-lib-types", "check-ci": "yarn run check"}, "private": true, "dependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.0.0-0", "@babel/plugin-proposal-optional-chaining": "^7.0.0-0", "@babel/plugin-proposal-private-methods": "*", "@babel/plugin-transform-arrow-functions": "^7.0.0-0", "@babel/plugin-transform-shorthand-properties": "^7.0.0-0", "@babel/plugin-transform-template-literals": "^7.0.0-0", "@babel/preset-env": "^7.1.6", "@ethersproject/shims": "^5.7.0", "@functionland/fula-sec": "^2.0.0", "@functionland/react-native-fula": "1.55.11", "@gorhom/bottom-sheet": "^4.5.1", "@metamask/sdk": "^0.30.0", "@metamask/sdk-react": "^0.30.0", "@notifee/react-native": "^7.8.2", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-clipboard/clipboard": "^1.12.1", "@react-native-community/netinfo": "9.3.10", "@react-native-community/slider": "4.4.2", "@react-native-firebase/app": "^18.7.1", "@react-native-firebase/crashlytics": "^18.7.1", "@react-native-picker/picker": "2.4.10", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/material-top-tabs": "^6.6.5", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/stack": "^6.3.20", "@react-spring/three": "^9.7.3", "@react-three/fiber": "^8.15.11", "@rneui/base": "^4.0.0-rc.8", "@rneui/themed": "^4.0.0-rc.8", "@shopify/react-native-skia": "0.1.196", "@shopify/restyle": "^2.4.2", "@visx/scale": "^3.5.0", "@walletconnect/encoding": "^1.0.2", "@walletconnect/react-native-compat": "^2.10.5", "@web3modal/core-react-native": "*", "@web3modal/wagmi-react-native": "^1.1.1", "axios": "^1.6.2", "big-integer": "^1.6.52", "eciesjs": "^0.4.6", "ethers": "5.7.2", "expo": "49.0.21", "expo-asset": "~8.10.1", "expo-font": "~11.4.0", "expo-gl": "~13.0.1", "expo-three": "^7.0.0", "i18next": "^24.2.2", "i18next-resources-to-backend": "^1.2.1", "lodash": "^4.17.21", "moment": "^2.29.4", "node-libs-react-native": "^1.2.1", "react": "18.2.0", "react-content-loader": "^6.2.1", "react-dom": "18.2.0", "react-i18next": "^15.4.1", "react-native": "0.72.10", "react-native-background-timer": "^2.4.1", "react-native-ble-manager": "^11.5.7", "react-native-device-info": "^10.12.0", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "~2.12.0", "react-native-get-random-values": "~1.9.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keychain": "^9.0.0", "react-native-localize": "^3.0.4", "react-native-modal": "^13.0.1", "react-native-pager-view": "6.2.0", "react-native-paper": "*", "react-native-permissions": "^5.0.0", "react-native-qrcode-svg": "^6.2.0", "react-native-randombytes": "^3.6.1", "react-native-reanimated": "~3.3.0", "react-native-reanimated-carousel": "^3.5.1", "react-native-redash": "^18.1.1", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-svg": "13.9.0", "react-native-svg-transformer": "^1.1.0", "react-native-syntax-highlighter": "^2.1.0", "react-native-tab-view": "^3.5.2", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "react-native-webview": "13.2.2", "react-native-wheel-color-picker": "^1.2.0", "react-native-wifi-reborn": "^4.13.0", "react-native-zeroconf": "^0.13.8", "text-encoding-polyfill": "^0.6.7", "three": "^0.145.0", "tslib": "^2.6.2", "viem": "1.21.4", "wagmi": "1.4.13", "whatwg-fetch": "^3.6.20", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-transform-class-static-block": "^7.26.0", "@nrwl/js": "17.3.1", "@nrwl/workspace": "17.3.1", "@nx/detox": "17.3.1", "@nx/eslint": "17.3.1", "@nx/eslint-plugin": "17.3.1", "@nx/jest": "17.3.1", "@nx/react-native": "17.3.1", "@nx/web": "17.3.1", "@react-native-community/cli": "12.2.0", "@react-native-community/cli-platform-android": "12.2.0", "@react-native-community/cli-platform-ios": "12.2.0", "@react-native-community/eslint-config": "^3.0.2", "@react-native/metro-config": "^0.73.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/jest-native": "5.4.3", "@testing-library/react-native": "12.4.1", "@types/axios": "^0.14.0", "@types/jest": "29.5.10", "@types/lodash": "^4.14.202", "@types/node": "^18.16.9", "@types/react": "18.2.39", "@types/react-native-background-timer": "^2.0.2", "@types/three": "^0.159.0", "@typescript-eslint/eslint-plugin": "6.13.2", "@typescript-eslint/parser": "6.13.2", "babel-jest": "29.7.0", "detox": "20.13.5", "eslint": "8.48.0", "eslint-config-prettier": "9.0.0", "eslint-plugin-import": "2.29.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-jsx-a11y": "6.8.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "jest": "29.7.0", "jest-circus": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-react-native": "18.0.0", "metro": "0.80.1", "metro-babel-register": "0.80.1", "metro-react-native-babel-preset": "0.77.0", "metro-resolver": "0.80.1", "nx": "17.3.1", "prettier": "3.1.0", "react-native-config": "1.5.1", "react-test-renderer": "18.2.0", "ts-jest": "29.1.1", "ts-node": "10.9.1", "typescript": "5.3.2"}, "resolutions": {"react-native-svg": "12.3.0", "react-native-redash": "^17.0.0"}, "packageManager": "yarn@3.6.4+sha512.e70835d4d6d62c07be76b3c1529cb640c7443f0fe434ef4b6478a5a399218cbaf1511b396b3c56eb03bc86424cff2320f6167ad2fde273aa0df6e60b7754029f"}