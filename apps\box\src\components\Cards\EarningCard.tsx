import React, { useState } from 'react';
import { useSDK } from '@metamask/sdk-react';
import {
  FxBox,
  FxCard,
  FxRefreshIcon,
  FxText,
  useFxTheme,
  FxButton,
  useToast,
  FxMoveIcon,
  FxTextInput,
  FxCopyIcon,
  FxPressableOpacity,
} from '@functionland/component-library';
import { BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import { ActivityIndicator, Alert, StyleSheet } from 'react-native';
import { copyFromClipboard } from '../../utils/clipboard';
import { useFulaBalance, useFormattedFulaBalance } from '../../hooks/useFulaBalance';
import { useContractIntegration } from '../../hooks/useContractIntegration';

type EarningCardProps = React.ComponentProps<typeof FxBox> & {
  data: { totalFula: string };
  loading?: boolean;
  onRefreshPress?: () => void;
};
export const EarningCard = ({
  data,
  loading,
  onRefreshPress,
  ...rest
}: EarningCardProps) => {
  const bottomSheetRef = React.useRef<BottomSheetModalMethods>(null);
  const { totalFula } = data;
  const { colors } = useFxTheme();
  const { queueToast } = useToast();
  const [wallet, setWallet] = useState<string>('');
  const chain = 'mumbai';

  // Use the formatted balance hook to get balance data
  const balanceData = useFormattedFulaBalance();
  const {
    formattedBalance,
    loading: balanceLoading,
    tokenSymbol,
    error: balanceError
  } = balanceData;

  // Get refresh function from the base hook
  const { refreshBalance } = useFulaBalance();

  // MetaMask SDK for wallet connection
  const { sdk, connected, account, connecting } = useSDK();

  // Handler for refresh icon click
  const handleRefresh = async () => {
    if (!account) {
      try {
        await sdk?.connect();
        queueToast({
          type: 'success',
          title: 'Wallet Connected',
          message: 'MetaMask wallet connected successfully',
        });
      } catch (e: any) {
        queueToast({
          type: 'error',
          title: 'Wallet Connection Failed',
          message: typeof e === 'object' && 'message' in e ? e.message : 'Failed to connect wallet',
        });
        return;
      }
    }
    refreshBalance();
    onRefreshPress?.();
  };

  // Use contract integration for blockchain operations
  const { contractService } = useContractIntegration();

  const handlePaste = async () => {
    const text = await copyFromClipboard();
    setWallet(text);
  };
  return (
    <FxCard
      {...rest}
      onLongPress={() => bottomSheetRef.current?.present()}
      delayLongPress={200}
    >
      <FxBox flexDirection="row" justifyContent="space-between">
        <FxCard.Title marginBottom="8">Rewards</FxCard.Title>
        {(loading || balanceLoading) ? (
          <ActivityIndicator />
        ) : (
          <FxRefreshIcon
            fill={colors.content3}
            onPress={handleRefresh}
            disabled={!!connecting}
          />
        )}
      </FxBox>
      <FxCard.Row>
        <FxCard.Row.Title>Total {tokenSymbol}</FxCard.Row.Title>
        <FxCard.Row.Data>
          <FxBox style={styles.totalFulaContainer}>
            {balanceError ? (
              <FxText>Error loading balance</FxText>
            ) : (
              <FxText style={styles.totalFula}>{formattedBalance}</FxText>
            )}
          </FxBox>
        </FxCard.Row.Data>
      </FxCard.Row>
    </FxCard>
  );
};

const styles = StyleSheet.create({
  totalFulaContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  totalFula: {},
  superscript: {
    fontSize: 10, // Smaller font size for superscript notation
  },
});
