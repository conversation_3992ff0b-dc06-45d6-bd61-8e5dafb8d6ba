import { useState, useEffect, useCallback } from 'react';
import { useContractIntegration } from './useContractIntegration';
import { useUserProfileStore } from '../stores/useUserProfileStore';
import { ethers } from 'ethers';

export interface ClaimableTokensState {
  claimableAmount: string;
  loading: boolean;
  error: string | null;
  canClaim: boolean;
}

export const useClaimableTokens = () => {
  const { contractService, isReady } = useContractIntegration();
  const appPeerId = useUserProfileStore((state) => state.appPeerId);
  
  const [state, setState] = useState<ClaimableTokensState>({
    claimableAmount: '0',
    loading: false,
    error: null,
    canClaim: false,
  });

  const fetchClaimableTokens = useCallback(async () => {
    if (!contractService || !isReady || !appPeerId) {
      setState(prev => ({
        ...prev,
        claimableAmount: '0',
        canClaim: false,
        loading: false,
        error: null,
      }));
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const claimableAmount = await contractService.claimableTokens(appPeerId);
      const canClaim = parseFloat(claimableAmount) > 0;
      
      setState({
        claimableAmount,
        loading: false,
        error: null,
        canClaim,
      });
    } catch (error) {
      console.error('Error fetching claimable tokens:', error);
      setState({
        claimableAmount: '0',
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch claimable tokens',
        canClaim: false,
      });
    }
  }, [contractService, isReady, appPeerId]);

  const claimTokens = useCallback(async () => {
    if (!contractService || !isReady || !appPeerId || !state.canClaim) {
      throw new Error('Cannot claim tokens: contract not ready or no claimable amount');
    }

    try {
      await contractService.claimTokens(appPeerId);
      // Refresh claimable tokens after successful claim
      await fetchClaimableTokens();
      return true;
    } catch (error) {
      console.error('Error claiming tokens:', error);
      throw error;
    }
  }, [contractService, isReady, appPeerId, state.canClaim, fetchClaimableTokens]);

  // Auto-fetch when dependencies change
  useEffect(() => {
    fetchClaimableTokens();
  }, [fetchClaimableTokens]);

  return {
    ...state,
    fetchClaimableTokens,
    claimTokens,
    formattedClaimableAmount: parseFloat(state.claimableAmount).toFixed(4),
  };
};
