use_modular_headers!

# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, '14.0'
prepare_react_native_project!

# If you are using a `react-native-flipper` your iOS build will fail when `NO_FLIPPER=1` is set.
# because `react-native-flipper` depends on (FlipperKit,...) that will be excluded
#
# To fix this you can also exclude `react-native-flipper` using a `react-native.config.js`
# ```js
# module.exports = {
#   dependencies: {
#     ...(process.env.NO_FLIPPER ? { 'react-native-flipper': { platforms: { ios: null } } } : {}),
# ```
flipper_config = ENV['NO_FLIPPER'] == "1" ? FlipperConfiguration.disabled : FlipperConfiguration.enabled

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'Box' do
  pod 'Fula', :podspec => 'https://raw.githubusercontent.com/functionland/go-fula/main/Fula.podspec'
  pod 'glog', :podspec => '../node_modules/react-native/third-party-podspecs/glog.podspec', :modular_headers => false
  config = use_native_modules!

  # Flags change depending on the env values.
  flags = get_default_flags()

  use_react_native!(
    :path => config[:reactNativePath],
    # Hermes is now enabled by default. Disable by setting this flag to false.
    :hermes_enabled => true,
    :fabric_enabled => flags[:fabric_enabled],
    # Enables Flipper.
    #
    # Note that if you have use_frameworks! enabled, Flipper will not work and
    # you should disable the next line.
    #:flipper_configuration => flipper_config,
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'BoxTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    PLIST_BUDDY_PATH = '/usr/libexec/PlistBuddy'
    # https://github.com/facebook/react-native/issues/34997
    `sed -i -e  $'s/__IPHONE_10_0/__IPHONE_13_0/' #{installer.sandbox.root}/RCT-Folly/folly/portability/Time.h`
    # https://github.com/facebook/react-native/issues/43335
    installer.pods_project.targets.each do |target|
      # https://stackoverflow.com/questions/74517344/when-compiling-with-xcode14-i-get-error-unsupported-swift-architecture-how-to-s
      target.build_configurations.each do |config|
        config.build_settings['ONLY_ACTIVE_ARCH'] = 'NO'
        config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)', '_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION']
      end
      if target.name == 'Flipper'
        file_path = 'Pods/Flipper/xplat/Flipper/FlipperTransportTypes.h'
        contents = File.read(file_path)
        unless contents.include?('#include <functional>')
          File.open(file_path, 'w') do |file|
            file.puts('#include <functional>')
            file.puts(contents)
          end
        end
      end
      if target.name == "hermes-engine"
        installer.pods_project.files.each do |fileref|
          if fileref.path.end_with? "hermes.xcframework"
            hermes_plist_file = "#{fileref.real_path}/Info.plist"
            # Patch Hermes to remove the debug symbols entry from the Info.plist (as it's not shipped with it)
            # This might be removed once Hermes starts to ship with Debug symbols or we remove our
            # direct dependency from the Main iOS target on "hermes.xcframework"
            Open3.capture3(PLIST_BUDDY_PATH, '-c', 'Delete :AvailableLibraries:0:DebugSymbolsPath', hermes_plist_file)
            Open3.capture3(PLIST_BUDDY_PATH, '-c', 'Delete :AvailableLibraries:1:DebugSymbolsPath', hermes_plist_file)
            Open3.capture3(PLIST_BUDDY_PATH, '-c', 'Delete :AvailableLibraries:2:DebugSymbolsPath', hermes_plist_file)
          end
        end
      end
    end
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false
    )
  end
end
